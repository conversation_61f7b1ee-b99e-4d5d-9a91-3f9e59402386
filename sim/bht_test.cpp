#include "NBody.hpp"

const double tolerance = std::pow(10.0,-std::numeric_limits<double>::digits10);

int main() {

	std::random_device rd;
	std::mt19937 gen(rd());
	std::uniform_real_distribution<> dis(-1.0, 1.0);

	constexpr size_t N = 1000;
	nblib::CollectionArray<double, N> col;

	// Create a collection of random particles
	for(size_t i=0; i<N; i++){
		col.Position[i] = nblib::Vector3D(dis(gen), dis(gen), dis(gen));
		col.Velocity[i] = nblib::Vector3D(0,0,0);
		col.Acceleration[i] = nblib::Vector3D(0.0, 0.0, 0.0);
		col.Mass[i] = 1.0;
	}

	// Creat a BHTree and store the collection
	nblib::Leaf<double> bhtree;
	StoreCollectionArray(col, bhtree);

	// Make a copy of the tree in vector form
	std::vector<nblib::Leaf<double>::LeafToParticle> particles;
	bhtree.DumpToVector(particles);


	
	double residuals = 0.0;
	for(size_t j=0; j<N; j++) {
		auto pos = particles[j].Position;
		residuals += col.Position[j].X - pos.X;
		residuals += col.Position[j].Y - pos.Y;
		residuals += col.Position[j].Z - pos.Z;
	}
	residuals /= 3.0*N;
	if(std::abs(residuals)>tolerance) {
		throw std::runtime_error("BHT test FAILED");
		std::cout << "BHT tested with " << N << " particles stored, but not and retrieved correctly!\n";
		return EXIT_FAILURE;
	}
	std::cout << "BHT tested with " << N << " particles stored and retrieved correctly. [PASSED]\n";

	size_t count = 0;
	for(size_t j=0; j<N; j++) {
		auto isLeaf = static_cast<nblib::Leaf<double>*>(particles[j].TreeNode)->IsLeaf();
		if(isLeaf) count++;
	}
	if(count!=N) {
		throw std::runtime_error("BHT test FAILED");
		std::cout << "BHT tested with " << N << " particles stored, but only " << count << " are leaves!\n";
		return EXIT_FAILURE;
	}else {
		std::cout << "BHT tested with " << N << " particles stored, and all are leaves. [PASSED]\n";
	}

	return EXIT_SUCCESS;
}