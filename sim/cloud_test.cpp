#include "NBody.hpp"
#include "TGraph2D.h"
#include "TApplication.h"
#include "TCanvas.h"

int main() {

	std::random_device rd;
	std::mt19937 gen(rd());
	std::uniform_real_distribution<> dis(-1.0, 1.0);

	constexpr size_t N = 1000;
	nblib::CollectionArray<double, N> col;

	for(size_t i=0; i<N; i++){
		col.Position[i] = nblib::Vector3D(dis(gen), dis(gen), dis(gen));
		col.Velocity[i] = nblib::Vector3D(0,0,0);
		col.Acceleration[i] = nblib::Vector3D(0.0, 0.0, 0.0);
		col.Mass[i] = 1.0;
	}

	nblib::Leaf<double> bhtree;

	for(size_t j=0; j<N; j++)
	{
		auto result = bhtree.Store(j,col.Position[j], col.Mass[j]);
		col.TreeNodes[j] = result.Placed;
		if(result.MovedIndex!=-1) col.TreeNodes[result.MovedIndex] = result.Moved;
	}
	
	std::vector<nblib::Vector3D> pos;
	bhtree.DumpToVector(pos);

	auto canvas = new TCanvas("","",800,800);
	auto graph = new TGraph2D(N, &pos[0].X, &pos[0].Y, &pos[0].Z);
	graph->Draw("p");
	canvas->SaveAs("test.gif");
	
	
	// Update accelerations with default parameters
	nblib::UpdateGravitationalAccelerations(&bhtree, col);  // Note the & before bhtree

	// Or with custom parameters
	nblib::UpdateAllAccelerations(&bhtree, col, 0.3, 6.67e-11, 0.001);  // Note the & before bhtree
  
	canvas->SaveAs("test.gif+");

	delete graph, canvas;

	return EXIT_SUCCESS;
}