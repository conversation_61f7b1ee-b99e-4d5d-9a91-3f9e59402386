#include "NBody.hpp"
#include "Random.hpp"
#include "TH2D.h"
#include "TCanvas.h"

int main() {

    const uint_fast32_t seed = 23041979;
	std::random_device rd;
	std::mt19937 gen(rd());
    gen.seed(seed);
	std::uniform_real_distribution<> dis(-1.0, 1.0);

    auto canvas = new TCanvas("","",600,600);
    auto map    = new TH2D("","",100,-1,1,100,-1,1);

    constexpr size_t N = 1<<16;
	nblib::CollectionArray<double, N> col;

	// Create a collection of random particles
	for(size_t i=0; i<N; i++){
		col.Position[i] = nblib::HomogeniousSphere(1.0, gen);
		col.Velocity[i] = nblib::Vector3D(0,0,0);
		col.Acceleration[i] = nblib::Vector3D(0.0, 0.0, 0.0);
		col.Mass[i] = 1.0;
	}

	// Creat a BHTree and store the collection
	nblib::Leaf<double> bhtree;
	StoreCollectionArray(col, bhtree);

    // Make a copy of the tree in vector form
	std::vector<nblib::Leaf<double>::LeafToParticle> particles;
	bhtree.DumpToVector(particles);


	double residuals = 0.0;
	for(size_t j=0; j<N; j++) {
		auto pos = particles[j].Position;
        if(std::abs(pos.Z)<1) map->Fill(pos.X,pos.Y);
    }


    map->Draw("colzp");
    canvas->SaveAs("gravity_map.png");
    return EXIT_SUCCESS;
}