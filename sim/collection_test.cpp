#include "Collection.hpp"
#include <random>
#include <iostream>
#include <limits>

int main(int argc, char** argv) {
	
	std::random_device rd;
	std::mt19937 gen(rd());
	std::uniform_real_distribution<> dis(-1.0, 1.0);

	constexpr size_t N = 1<<10;

	nblib::SPCollectionD<N> col;
    for(size_t i=0; i<N; i++){
		col.Position[i] = nblib::Vector3D(dis(gen), dis(gen), dis(gen));
		col.Velocity[i] = nblib::Vector3D(0,0,0);
		col.Acceleration[i] = nblib::Vector3D(0.0, 0.0, 0.0);
	}

	return 0;
}
