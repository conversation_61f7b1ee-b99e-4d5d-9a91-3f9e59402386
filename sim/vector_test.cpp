
#include <random>
#include <iostream>
#include <limits>
#include <cmath>
#include "Vector.hpp"

int main(int argc, char** argv) {

	double tol = std::pow(10.0,-std::numeric_limits<double>::digits10);
	
	std::random_device rd;
	std::mt19937 gen(rd());
	std::uniform_real_distribution<> dis(-1.0, 1.0);

	nblib::Vector3D vec1(dis(gen), dis(gen), dis(gen));
	auto norm = vec1.Norm();
	auto nVec1 = ~vec1;
	auto checkOne  = nVec1.Norm();

	nblib::Vector3D vec2(dis(gen), dis(gen), dis(gen));
	auto nVec2 = ~vec2;
	
	auto vec3 = nVec1.Cross(nVec2);
	auto nVec3 = ~vec3;

	auto checkZero1 = nVec1.Dot(nVec3);
	auto checkZero2 = nVec2.Dot(nVec3);

	auto vec4 = nVec1 - nVec2;
	auto vec5 = nVec1*(-1.0) + nVec2;
	auto vec6 = vec4 + vec5;
	auto checkZero3 = vec6.Norm2();
	
	auto checkZero0 = double(1.0)-checkOne;

	std::cout << "Check 1 " << (std::abs(checkZero0)<tol?"OK":"FAIL") << "\n";
	std::cout << "Check 2 " << (std::abs(checkZero1)<tol?"OK":"FAIL") << "\n";
	std::cout << "Check 3 " << (std::abs(checkZero2)<tol?"OK":"FAIL") << "\n";
	std::cout << "Check 4 " << (std::abs(checkZero3)<tol?"OK":"FAIL") << "\n";

	return 0;
}