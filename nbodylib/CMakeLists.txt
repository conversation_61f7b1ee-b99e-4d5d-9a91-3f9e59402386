cmake_minimum_required(VERSION 3.16)
project(NBodySimulation VERSION 1.0.0)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set build type to Release for performance
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler flags for optimization and warnings
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG -march=native")
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -Wall -Wextra -Wpedantic")

# Find required packages
find_package(Threads REQUIRED)

# Optional: Find ROOT for animation tools
find_package(ROOT QUIET)
if(ROOT_FOUND)
    message(STATUS "ROOT found: ${ROOT_VERSION}")
    set(HAVE_ROOT TRUE)
else()
    message(STATUS "ROOT not found - animation tools will not be built")
    set(HAVE_ROOT FALSE)
endif()

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/..)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/src)

# Physics library source files
set(PHYSICS_SOURCES
    ${CMAKE_CURRENT_SOURCE_DIR}/../sim/GravityInterface.cpp
)

# ============================================================================
# MAIN N-BODY SIMULATION EXECUTABLE
# ============================================================================
add_executable(nbody_simulation
    src/simulation_1000_particles.cpp
    ${PHYSICS_SOURCES}
)

target_link_libraries(nbody_simulation 
    Threads::Threads
)

# Set output directory
set_target_properties(nbody_simulation PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/bin
)

# ============================================================================
# DATA PROCESSING UTILITIES
# ============================================================================

# Data analysis utility
add_executable(data_analyzer
    src/data_analyzer.cpp
    ${PHYSICS_SOURCES}
)

target_link_libraries(data_analyzer 
    Threads::Threads
)

set_target_properties(data_analyzer PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/bin
)

# ============================================================================
# ANIMATION TOOLS (if ROOT is available)
# ============================================================================
if(HAVE_ROOT)
    # ROOT animation executable
    add_executable(root_animator
        animation/animate_simulation.C
    )
    
    target_include_directories(root_animator PRIVATE ${ROOT_INCLUDE_DIRS})
    target_link_libraries(root_animator ${ROOT_LIBRARIES})
    target_compile_definitions(root_animator PRIVATE ${ROOT_CXX_FLAGS})
    
    set_target_properties(root_animator PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/bin
    )
endif()

# ============================================================================
# CUSTOM TARGETS FOR RUNNING SIMULATIONS
# ============================================================================

# Quick test simulation (100 particles, 10 years)
add_custom_target(quick_sim
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/bin/nbody_simulation 100 0.1 1e-6 10.0
    DEPENDS nbody_simulation
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
    COMMENT "Running quick N-body simulation (100 particles, 10 years)..."
)

# Standard simulation (1000 particles, 120 years)
add_custom_target(standard_sim
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/bin/nbody_simulation
    DEPENDS nbody_simulation
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
    COMMENT "Running standard N-body simulation (1000 particles, 120 years)..."
)

# Large simulation (5000 particles, 120 years)
add_custom_target(large_sim
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/bin/nbody_simulation 5000 0.1 1e-6 120.0
    DEPENDS nbody_simulation
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
    COMMENT "Running large N-body simulation (5000 particles, 120 years)..."
)

# Data analysis target
add_custom_target(analyze_data
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/bin/data_analyzer
    DEPENDS data_analyzer
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
    COMMENT "Analyzing simulation data..."
)

# Animation generation (if ROOT available)
if(HAVE_ROOT)
    add_custom_target(generate_animation
        COMMAND ${CMAKE_CURRENT_SOURCE_DIR}/scripts/generate_animation.sh
        DEPENDS nbody_simulation
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Generating animation from simulation data..."
    )
endif()

# Complete workflow: simulate + analyze + animate
if(HAVE_ROOT)
    add_custom_target(full_workflow
        COMMAND ${CMAKE_CURRENT_BINARY_DIR}/bin/nbody_simulation
        COMMAND ${CMAKE_CURRENT_BINARY_DIR}/bin/data_analyzer
        COMMAND ${CMAKE_CURRENT_SOURCE_DIR}/scripts/generate_animation.sh
        DEPENDS nbody_simulation data_analyzer
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Running complete N-body simulation workflow..."
    )
else()
    add_custom_target(full_workflow
        COMMAND ${CMAKE_CURRENT_BINARY_DIR}/bin/nbody_simulation
        COMMAND ${CMAKE_CURRENT_BINARY_DIR}/bin/data_analyzer
        DEPENDS nbody_simulation data_analyzer
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Running N-body simulation and analysis (no animation - ROOT not found)..."
    )
endif()

# ============================================================================
# INSTALLATION
# ============================================================================

# Install executables
install(TARGETS nbody_simulation data_analyzer
    RUNTIME DESTINATION bin
)

if(HAVE_ROOT)
    install(TARGETS root_animator
        RUNTIME DESTINATION bin
    )
endif()

# Install scripts
install(DIRECTORY scripts/
    DESTINATION scripts
    FILES_MATCHING PATTERN "*.sh"
    PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE
)

# Install animation tools
install(DIRECTORY animation/
    DESTINATION animation
    FILES_MATCHING PATTERN "*.C" PATTERN "*.md"
)

# Install documentation
install(DIRECTORY docs/
    DESTINATION docs
    FILES_MATCHING PATTERN "*.md" PATTERN "*.txt"
)

# ============================================================================
# TESTING
# ============================================================================

# Enable CTest
enable_testing()

# Add basic simulation test
add_test(NAME QuickSimulation 
    COMMAND nbody_simulation 10 0.1 1e-6 1.0
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/bin
)

# Set test properties
set_tests_properties(QuickSimulation PROPERTIES TIMEOUT 60)

# ============================================================================
# DOCUMENTATION AND HELP
# ============================================================================

# Print build information
message(STATUS "")
message(STATUS "N-Body Simulation Configuration:")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Compiler: ${CMAKE_CXX_COMPILER_ID} ${CMAKE_CXX_COMPILER_VERSION}")
message(STATUS "  ROOT support: ${HAVE_ROOT}")
if(HAVE_ROOT)
    message(STATUS "  ROOT version: ${ROOT_VERSION}")
endif()
message(STATUS "")
message(STATUS "Available targets:")
message(STATUS "  make nbody_simulation    - Build main simulation")
message(STATUS "  make data_analyzer       - Build data analysis tool")
if(HAVE_ROOT)
    message(STATUS "  make root_animator       - Build ROOT animation tool")
endif()
message(STATUS "  make quick_sim          - Run quick test (100 particles, 10 years)")
message(STATUS "  make standard_sim       - Run standard simulation (1000 particles)")
message(STATUS "  make large_sim          - Run large simulation (5000 particles)")
message(STATUS "  make analyze_data       - Analyze simulation results")
if(HAVE_ROOT)
    message(STATUS "  make generate_animation - Create animation from data")
endif()
message(STATUS "  make full_workflow      - Run complete simulation workflow")
message(STATUS "  make test               - Run test suite")
message(STATUS "")
