#pragma once

#include <random>
#include <cmath>
#include "Vector.hpp"
namespace nblib {

	// Sphere (Normalized Gaussian Method)
	template<class FloatType>
	Vector3<FloatType> HomogeniousSphere(const FloatType &radius, std::mt19937& gen) {
		std::normal_distribution<FloatType> dist(0.0, 1.0);
		std::uniform_real_distribution<FloatType> uniform(0.0, 1.0);
		
		// Generate point on unit sphere using Gaussian
		Vector3<FloatType> p(dist(gen), dist(gen), dist(gen));
		
		// Normalize to unit sphere
		p.Normalize();
		
		// Scale by random radius (with correct distribution)
		auto r = radius * std::cbrt(uniform(gen));
		p *= r;

		return p;
	}

}