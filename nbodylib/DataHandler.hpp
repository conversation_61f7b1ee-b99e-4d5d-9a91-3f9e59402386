#pragma once
#include <vector>
#include <memory>
#include <initializer_list>

namespace dmh{

	enum neighbors { kXM, kXP, kYM, kYP, kZM, kZP, kAllNeighbors};

	template<typename Type>
	struct Cell{

		std::vector<Type*> Items;
		Cell* Neighbors[kAllNeighbors];

		inline void Add(Type &m) { Items.push_back(&m); }
		inline uint64_t GetN() { return Items.size(); }

	};

	template<class Type, uint64_t tDim>
	class Grid{

		// Filling in the following order:
		// From -X to +X -> X major
		// From -Y to +Y -> Y intermediate
		// From -Z to +Z -> Z minor

		private:
		
		double Min[3], Max[3];

		public:

		double HalfSize = 0.5;

		std::vector<Cell<Type>> Cells;
		static constexpr uint64_t GetDimension() { return tDim; }
		static constexpr uint64_t GetPlaneSize() { return tDim*tDim; }
		static constexpr uint64_t GetWorldSize() { return tDim*tDim*tDim; }

		static int64_t GetXPneighbor(const uint64_t &index) {return index+1;}
		static int64_t GetXMneighbor(const uint64_t &index) {return index-1;}
		static int64_t GetYPneighbor(const uint64_t &index) {return index+GetDimension();}
		static int64_t GetYMneighbor(const uint64_t &index) {return index-GetDimension();}
		static int64_t GetZPneighbor(const uint64_t &index) {return index+GetPlaneSize();}
		static int64_t GetZMneighbor(const uint64_t &index) {return index-GetPlaneSize();}

		Grid(const double &halfsize) : HalfSize(halfsize) {

			Cells.resize(GetWorldSize());

			for(auto i=0; i<GetWorldSize(); i++) {
				for(auto &neighbor :Cells[i].Neighbors) neighbor = nullptr;

				auto s = GetWorldSize();
				if(GetXMneighbor(i)>=0) Cells[i].Neighbors[kXM] = &Cells[GetXMneighbor(i)];
				if(GetXPneighbor(i)<s)  Cells[i].Neighbors[kXP] = &Cells[GetXPneighbor(i)];
				if(GetYMneighbor(i)>=0) Cells[i].Neighbors[kYM] = &Cells[GetYMneighbor(i)];
				if(GetYPneighbor(i)<s)  Cells[i].Neighbors[kYP] = &Cells[GetYPneighbor(i)];
				if(GetZMneighbor(i)>=0) Cells[i].Neighbors[kZM] = &Cells[GetZMneighbor(i)];
				if(GetZPneighbor(i)<s)  Cells[i].Neighbors[kZP] = &Cells[GetZPneighbor(i)];
			}

		}

		inline Cell<Type>* operator() (const uint64_t &iX,const uint64_t &iY,const uint64_t &iZ) {
			return &Cells[ iX+iY*GetDimension()+iZ*GetPlaneSize() ];
		}

		uint64_t FindCell(const Type &entity){

			double delta = 2.0*HalfSize/GetDimension();

			uint64_t iX = (entity.Position[0]+HalfSize)/delta;
			uint64_t iY = (entity.Position[1]+HalfSize)/delta;
			uint64_t iZ = (entity.Position[2]+HalfSize)/delta;
			
			return iX+iY*GetDimension()+iZ*GetPlaneSize();
		}


		uint64_t Fill(std::vector<Type> &samples){

			uint64_t outside = 0;
			for(auto &aSample : samples) {
				auto index = FindCell(aSample);
				if(index<GetWorldSize()) Cells[index].Items.push_back(&aSample);
				else outside++;
			}
			return outside;
		}
	};

}