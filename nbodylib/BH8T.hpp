#pragma once
#include "Collection.hpp"
#include "Vector.hpp"
#include <array>
#include <vector>
#include <algorithm>
#include <functional>

namespace nblib {


	enum {
		// XYZ
		kNNN = 0b000, kNNP = 0b001, kNPN = 0b010, kNPP = 0b011,
		kPNN = 0b100, kPNP = 0b101, kPPN = 0b110, kPPP = 0b111
	};

	const std::array<std::string, 8> Octant = {"NNN", "NNP", "NPN", "NPP", "PNN", "PNP", "PPN", "PPP"};

	template<class FloatType>
	inline uint8_t GetOctant(const Vector3<FloatType> &position, Vector3<FloatType> &center) noexcept {
		uint8_t octant = 0;
		if (position.X > center.X) octant |= kPNN;
		if (position.Y > center.Y) octant |= kNPN;
		if (position.Z > center.Z) octant |= kNNP;
		return octant;
	}

	template<class FloatType>
	struct Leaf {
		
		uint64_t Depth;
		Vector3<FloatType> Center;
		FloatType Mass;
		Vector3<FloatType> MassWeightedPosition;
		
		
		size_t ParticleIndex;
		std::array<Leaf<FloatType>*, 8> Branches;

		Leaf(uint64_t depth=0) : 
			Depth(depth),
			Center(0, 0, 0), 
			Mass(0), 
			MassWeightedPosition(0, 0, 0), 
			ParticleIndex(-1)
			{ Branches.fill(nullptr); }
		
		inline FloatType CenterOfMass() const { return MassWeightedPosition/Mass; }
		inline FloatType Size() const { return std::pow(2., -FloatType(Depth) ); }

		inline bool IsLeaf() 	const { return ParticleIndex!=-1; }
		inline bool IsEmpty() 	const { return Mass==FloatType(0); }
		inline bool IsBranch() 	const { return !IsEmpty() && !IsLeaf(); }
		inline bool IsRoot() 	const { return Depth==0; }

		~Leaf() { for (auto &node : Branches) { delete node; } }

		struct StoredWhere {
			Leaf<FloatType>* Placed;
			Leaf<FloatType>* Moved;
			int64_t MovedIndex;

			StoredWhere() : Placed(nullptr), Moved(nullptr), MovedIndex(-1) {}
			StoredWhere(Leaf<FloatType>* placed) : Placed(placed), Moved(nullptr), MovedIndex(-1) {}
			void MovedParticle(int64_t index, Leaf<FloatType>* node) { Moved = node; MovedIndex = index; }
			Leaf<FloatType>* operator=(Leaf<FloatType>* placed) { Placed = placed; return *this; }
		};

		StoredWhere Store(size_t index, const Vector3<FloatType> &position, const FloatType &mass=1) {
			
			if(mass==FloatType(0)) {
				throw std::runtime_error("Cannot store a particle with zero mass");
				return StoredWhere();
			}

			// Is this a fresh new leaf?
			if(IsEmpty()) {
				
				// Store the particle's information
				ParticleIndex = index;
				Mass = mass;
				MassWeightedPosition = position*mass;
			
				return StoredWhere(this);
			}
						
			StoredWhere result;

			// If this is a leaf, we need to split it
			if (IsLeaf()) {
				
				auto octant = GetOctant(MassWeightedPosition*Mass, Center);				
				
				if(Branches[octant]==nullptr) {
				
					Branches[octant] = new Leaf<FloatType>(Depth+1);
					Branches[octant]->Center = Center;
					auto size = Branches[octant]->Size();
					Branches[octant]->Center.X += (octant & kPNN) ? size : -size;
					Branches[octant]->Center.Y += (octant & kNPN) ? size : -size;
					Branches[octant]->Center.Z += (octant & kNNP) ? size : -size;
				} else {
					throw std::runtime_error("A Leaf already had aBranch at octant " + Octant[octant]);
					return StoredWhere();
				}
				
				Branches[octant]->Store(ParticleIndex,MassWeightedPosition*Mass,Mass);
				result.Moved = Branches[octant];
				result.MovedIndex = ParticleIndex;
				ParticleIndex = -1;
			}
			
			// When it reaches this point, it is a branch
			Mass += mass;
			MassWeightedPosition += position*mass;

			auto octant = GetOctant(position, Center);

			// Create the branch if it doesn't exist
			if(Branches[octant]==nullptr) {
				Branches[octant] = new Leaf<FloatType>(Depth+1);
				auto size = Branches[octant]->Size();
				Branches[octant]->Center = Center;
				Branches[octant]->Center.X += (octant & kPNN) ? size : -size;
				Branches[octant]->Center.Y += (octant & kNPN) ? size : -size;
				Branches[octant]->Center.Z += (octant & kNNP) ? size : -size;
				
			}
			
			// Recurse to store particle in either an new or an existing branch
			result = Branches[octant]->Store(index, position, mass);
			return result;
		}

		struct LeafToParticle {
			size_t Index;
			Leaf<FloatType>* TreeNode;
			FloatType Mass;
			Vector3<FloatType> Position;
			LeafToParticle() = default;
			LeafToParticle(size_t index, Leaf<FloatType>* node, FloatType mass, Vector3<FloatType> position) : 
				Index(index), TreeNode(node), Mass(mass), Position(position) {}
		};

		void CollectLeaves(std::vector<LeafToParticle>& particles) {
			
			// If this is a leaf with a particle, add it to our collection
			if (IsLeaf()) {
				Vector3<FloatType> position = MassWeightedPosition / Mass;
				
				auto aParticle = LeafToParticle(ParticleIndex, this, Mass, position);
				particles.push_back(aParticle);
			}
			
			// Recursively process all branches
			for (const auto& branch : Branches) {
				if (branch != nullptr) {
					branch->CollectLeaves(particles);
				}
			}

		}


		void DumpToVector(std::vector<LeafToParticle>& particles) {


			CollectLeaves(particles);
			
			std::sort(particles.begin(), particles.end(), 
					[](const LeafToParticle& a, 
						const LeafToParticle& b) {
						return a.Index < b.Index;
					});
			
		}		

	};

	template<class FloatType, size_t Size>
	void StoreCollectionArray(CollectionArray<FloatType, Size> &col, Leaf<FloatType> &bhtree) {
		for(size_t j=0; j<Size; j++) {
			auto result = bhtree.Store(j,col.Position[j], col.Mass[j]);
			col.TreeNodes[j] = result.Placed;
			if(result.MovedIndex!=-1) col.TreeNodes[result.MovedIndex] = result.Moved;
		}
	}


}

	

