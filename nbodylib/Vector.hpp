#pragma once
#include <vector>
#include <random>
#include <iostream>
#include <cmath>
#include <stdexcept>

namespace nblib {

template<class FloatType>
struct Vector3 {
	FloatType X, Y, Z;

	// Constructors
	Vector3() noexcept : X(0), Y(0), Z(0) {}
	Vector3(FloatType x, FloatType y, FloatType z) noexcept : X(x), Y(y), Z(z) {}
	Vector3(const Vector3& other) noexcept = default;
	Vector3& operator=(const Vector3& other) noexcept = default;

	// Array access with bounds checking in debug mode
	FloatType& operator[](const uint8_t& index) noexcept {
		#ifdef DEBUG
		if (index > 2) throw std::out_of_range("Vector3 index out of bounds");
		#endif
		return *(&X + index);
	}

	FloatType operator[](const uint8_t& index) const noexcept {
		#ifdef DEBUG
		if (index > 2) throw std::out_of_range("Vector3 index out of bounds");
		#endif
		return *(&X + index);
	}

	// Basic vector operations
	FloatType Norm2() const noexcept { return X*X + Y*Y + Z*Z; }
	FloatType Norm() const noexcept { return sqrt(Norm2()); }

	// Normalize in place (modifies this vector)
	Vector3& Normalize() noexcept {
		FloatType norm = Norm();
		if (norm > 0) {
			*this /= norm;
		}
		return *this;
	}

	// Return normalized copy (doesn't modify this vector)
	Vector3 Normalized() const noexcept {
		Vector3 result(*this);
		return result.Normalize();
	}

	// Keep the old operator~ for backward compatibility
	Vector3 operator~() const noexcept {
		Vector3 result(*this);
		return result.Normalize();
	}

	// Compound assignment operators
	Vector3& operator+=(const Vector3& other) noexcept {
		X += other.X;
		Y += other.Y;
		Z += other.Z;
		return *this;
	}

	Vector3& operator-=(const Vector3& other) noexcept {
		X -= other.X;
		Y -= other.Y;
		Z -= other.Z;
		return *this;
	}

	Vector3& operator*=(const FloatType& other) noexcept {
		X *= other;
		Y *= other;
		Z *= other;
		return *this;
	}

	Vector3& operator/=(const FloatType& other) noexcept {
		FloatType inv = FloatType(1) / other;  // Optimize division with multiplication
		X *= inv;
		Y *= inv;
		Z *= inv;
		return *this;
	}

	// Binary operators (const and noexcept)
	Vector3 operator+(const Vector3& other) const noexcept {
		return Vector3(X + other.X, Y + other.Y, Z + other.Z);
	}

	Vector3 operator-(const Vector3& other) const noexcept {
		return Vector3(X - other.X, Y - other.Y, Z - other.Z);
	}

	Vector3 operator*(const FloatType& other) const noexcept {
		return Vector3(X * other, Y * other, Z * other);
	}

	Vector3 operator/(const FloatType& other) const noexcept {
		FloatType inv = FloatType(1) / other;  // Optimize division with multiplication
		return Vector3(X * inv, Y * inv, Z * inv);
	}

	// Additional useful vector operations
	FloatType Dot(const Vector3& other) const noexcept {
		return X * other.X + Y * other.Y + Z * other.Z;
	}

	Vector3 Cross(const Vector3& other) const noexcept {
		return Vector3(
			Y * other.Z - Z * other.Y,
			Z * other.X - X * other.Z,
			X * other.Y - Y * other.X
		);
	}

	FloatType Distance(const Vector3& other) const noexcept {
		return (*this - other).Norm();
	}

	FloatType DistanceSquared(const Vector3& other) const noexcept {
		return (*this - other).Norm2();
	}

};

typedef Vector3<double> Vector3D;
typedef Vector3<float> Vector3F;


}