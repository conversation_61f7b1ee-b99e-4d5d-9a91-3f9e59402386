#pragma once
#include "BH8T.hpp"
#include "Collection.hpp"
#include "Vector.hpp"
#include <cmath>

namespace nblib {

	// Default <PERSON>-<PERSON> parameters
	template<class FloatType>
	struct BarnesHutParams {
		static constexpr FloatType theta = FloatType(0.5);           // Opening angle criterion
		static constexpr FloatType G = FloatType(6.67430e-11);      // Gravitational constant
		static constexpr FloatType softening = FloatType(1e-3);     // Softening parameter
	};

	// Forward declaration for recursive force calculation
	template<class FloatType>
	Vector3<FloatType> CalculateForceFromNode(
		const Vector3<FloatType>& particlePos,
		const FloatType& particleMass,
		const Leaf<FloatType>* node,
		const FloatType& theta,
		const FloatType& G,
		const FloatType& softening
	) noexcept;

	/**
	 * @brief Update acceleration field of particles using Barnes-Hut algorithm
	 *
	 * This function implements the Barnes-Hu<PERSON> algorithm for efficient O(N log N)
	 * gravitational force calculation. It traverses the octree and updates the
	 * acceleration of each particle based on gravitational forces.
	 *
	 * @tparam FloatType The floating-point type (float, double, etc.)
	 * @param root Reference to the root of the Barnes-Hut tree
	 * @param theta Opening angle criterion (default: 0.5)
	 * @param G Gravitational constant (default: 6.67430e-11)
	 * @param softening Softening parameter to avoid singularities (default: 1e-3)
	 */
	template<class FloatType>
	void UpdateAcceleration(
		Leaf<FloatType>& root,
		const FloatType& theta = BarnesHutParams<FloatType>::theta,
		const FloatType& G = BarnesHutParams<FloatType>::G,
		const FloatType& softening = BarnesHutParams<FloatType>::softening
	) noexcept {

		// Collect all particles from the tree
		std::vector<typename Leaf<FloatType>::LeafToParticle> particles;
		root.DumpToVector(particles);

		// Calculate forces for each particle
		for (auto& particle : particles) {
			if (particle.TreeNode == nullptr) continue;

			// Reset acceleration
			Vector3<FloatType> acceleration(0, 0, 0);

			// Calculate total force from all other masses in the tree
			Vector3<FloatType> totalForce = CalculateForceFromNode(
				particle.Position,
				particle.Mass,
				&root,
				theta,
				G,
				softening
			);

			// Convert force to acceleration (F = ma, so a = F/m)
			acceleration = totalForce / particle.Mass;

			// Store the acceleration back to the particle's tree node
			// Note: We need to access the collection to update acceleration
			// This will be handled by the wrapper functions that have access to collections
		}
	}

	/**
	 * @brief Calculate gravitational force from a tree node (recursive)
	 *
	 * This is the core of the Barnes-Hut algorithm. It recursively traverses
	 * the tree and calculates forces based on the opening angle criterion.
	 *
	 * @param particlePos Position of the particle experiencing the force
	 * @param particleMass Mass of the particle experiencing the force
	 * @param node Current tree node being evaluated
	 * @param theta Opening angle criterion
	 * @param G Gravitational constant
	 * @param softening Softening parameter
	 * @return Vector3<FloatType> Total gravitational force from this node
	 */
	template<class FloatType>
	Vector3<FloatType> CalculateForceFromNode(
		const Vector3<FloatType>& particlePos,
		const FloatType& particleMass,
		const Leaf<FloatType>* node,
		const FloatType& theta,
		const FloatType& G,
		const FloatType& softening
	) noexcept {

		if (node == nullptr || node->IsEmpty()) {
			return Vector3<FloatType>(0, 0, 0);
		}

		// Calculate center of mass position
		Vector3<FloatType> centerOfMass = node->MassWeightedPosition / node->Mass;

		// Calculate distance vector from particle to center of mass
		Vector3<FloatType> r = centerOfMass - particlePos;
		FloatType distance = r.Norm();

		// Apply softening to avoid singularities
		FloatType softenedDistance = std::sqrt(distance * distance + softening * softening);

		// If this is a leaf node with a single particle
		if (node->IsLeaf()) {
			// Don't calculate force on itself
			if (distance < softening * FloatType(0.1)) {
				return Vector3<FloatType>(0, 0, 0);
			}

			// Calculate gravitational force: F = G * m1 * m2 / r^2 * r_hat
			FloatType forceMagnitude = G * particleMass * node->Mass / (softenedDistance * softenedDistance * softenedDistance);
			return r * forceMagnitude;
		}

		// For internal nodes, apply Barnes-Hut criterion
		FloatType nodeSize = node->Size();

		// If node is far enough (s/d < theta), treat as single mass
		if (nodeSize / distance < theta) {
			FloatType forceMagnitude = G * particleMass * node->Mass / (softenedDistance * softenedDistance * softenedDistance);
			return r * forceMagnitude;
		}

		// Otherwise, recursively calculate forces from child nodes
		Vector3<FloatType> totalForce(0, 0, 0);
		for (const auto& branch : node->Branches) {
			if (branch != nullptr) {
				totalForce += CalculateForceFromNode(
					particlePos,
					particleMass,
					branch,
					theta,
					G,
					softening
				);
			}
		}

		return totalForce;
	}

	/**
	 * @brief Update accelerations for CollectionArray using Barnes-Hut algorithm
	 *
	 * This function updates the acceleration field of all particles in a CollectionArray
	 * using the Barnes-Hut tree for efficient force calculation.
	 *
	 * @tparam FloatType The floating-point type
	 * @tparam Size The size of the collection array
	 * @param root Pointer to the root of the Barnes-Hut tree
	 * @param collection Reference to the collection containing particle data
	 * @param theta Opening angle criterion (default: 0.5)
	 * @param G Gravitational constant (default: 6.67430e-11)
	 * @param softening Softening parameter (default: 1e-3)
	 */
	template<class FloatType, size_t Size>
	void UpdateAcceleration(
		Leaf<FloatType>* root,
		CollectionArray<FloatType, Size>& collection,
		const FloatType& theta = BarnesHutParams<FloatType>::theta,
		const FloatType& G = BarnesHutParams<FloatType>::G,
		const FloatType& softening = BarnesHutParams<FloatType>::softening
	) noexcept {

		if (root == nullptr) return;

		// Calculate forces for each particle in the collection
		for (size_t i = 0; i < Size; ++i) {
			// Reset acceleration
			collection.Acceleration[i] = Vector3<FloatType>(0, 0, 0);

			// Calculate total force from all other masses in the tree
			Vector3<FloatType> totalForce = CalculateForceFromNode(
				collection.Position[i],
				collection.Mass[i],
				root,
				theta,
				G,
				softening
			);

			// Convert force to acceleration (F = ma, so a = F/m)
			if (collection.Mass[i] > FloatType(0)) {
				collection.Acceleration[i] = totalForce / collection.Mass[i];
			}
		}
	}

	/**
	 * @brief Update accelerations for Collection using Barnes-Hut algorithm
	 *
	 * This function updates the acceleration field of all particles in a Collection
	 * using the Barnes-Hut tree for efficient force calculation.
	 *
	 * @tparam FloatType The floating-point type
	 * @param root Pointer to the root of the Barnes-Hut tree
	 * @param collection Reference to the collection containing particle data
	 * @param theta Opening angle criterion (default: 0.5)
	 * @param G Gravitational constant (default: 6.67430e-11)
	 * @param softening Softening parameter (default: 1e-3)
	 */
	template<class FloatType>
	void UpdateAcceleration(
		Leaf<FloatType>* root,
		Collection<FloatType>& collection,
		const FloatType& theta = BarnesHutParams<FloatType>::theta,
		const FloatType& G = BarnesHutParams<FloatType>::G,
		const FloatType& softening = BarnesHutParams<FloatType>::softening
	) noexcept {

		if (root == nullptr) return;

		size_t numParticles = collection.Size();

		// Calculate forces for each particle in the collection
		for (size_t i = 0; i < numParticles; ++i) {
			// Reset acceleration
			collection.Acceleration[i] = Vector3<FloatType>(0, 0, 0);

			// Calculate total force from all other masses in the tree
			Vector3<FloatType> totalForce = CalculateForceFromNode(
				collection.Position[i],
				collection.Mass[i],
				root,
				theta,
				G,
				softening
			);

			// Convert force to acceleration (F = ma, so a = F/m)
			if (collection.Mass[i] > FloatType(0)) {
				collection.Acceleration[i] = totalForce / collection.Mass[i];
			}
		}
	}

	/**
	 * @brief Convenience function for updating gravitational accelerations with default parameters
	 *
	 * @tparam FloatType The floating-point type
	 * @tparam Size The size of the collection array
	 * @param root Pointer to the root of the Barnes-Hut tree
	 * @param collection Reference to the collection containing particle data
	 */
	template<class FloatType, size_t Size>
	void UpdateGravitationalAccelerations(
		Leaf<FloatType>* root,
		CollectionArray<FloatType, Size>& collection
	) noexcept {
		UpdateAcceleration(root, collection);
	}

	/**
	 * @brief Convenience function for updating gravitational accelerations with default parameters
	 *
	 * @tparam FloatType The floating-point type
	 * @param root Pointer to the root of the Barnes-Hut tree
	 * @param collection Reference to the collection containing particle data
	 */
	template<class FloatType>
	void UpdateGravitationalAccelerations(
		Leaf<FloatType>* root,
		Collection<FloatType>& collection
	) noexcept {
		UpdateAcceleration(root, collection);
	}

	/**
	 * @brief Convenience function for updating accelerations with custom parameters
	 *
	 * @tparam FloatType The floating-point type
	 * @tparam Size The size of the collection array
	 * @param root Pointer to the root of the Barnes-Hut tree
	 * @param collection Reference to the collection containing particle data
	 * @param theta Opening angle criterion
	 * @param G Gravitational constant
	 * @param softening Softening parameter
	 */
	template<class FloatType, size_t Size>
	void UpdateAllAccelerations(
		Leaf<FloatType>* root,
		CollectionArray<FloatType, Size>& collection,
		const FloatType& theta,
		const FloatType& G,
		const FloatType& softening
	) noexcept {
		UpdateAcceleration(root, collection, theta, G, softening);
	}

	/**
	 * @brief Convenience function for updating accelerations with custom parameters
	 *
	 * @tparam FloatType The floating-point type
	 * @param root Pointer to the root of the Barnes-Hut tree
	 * @param collection Reference to the collection containing particle data
	 * @param theta Opening angle criterion
	 * @param G Gravitational constant
	 * @param softening Softening parameter
	 */
	template<class FloatType>
	void UpdateAllAccelerations(
		Leaf<FloatType>* root,
		Collection<FloatType>& collection,
		const FloatType& theta,
		const FloatType& G,
		const FloatType& softening
	) noexcept {
		UpdateAcceleration(root, collection, theta, G, softening);
	}

}