#pragma once
#include "BH8T.hpp"
#include "Collection.hpp"
#include "Vector.hpp"
#include <cmath>

namespace nblib {

	template<class FloatType>
	Vector3<FloatType> CalculateForceFromNode(
		const Vector3<FloatType>& particlePos,
		const FloatType& particleMass,
		const Leaf<FloatType>* node,
		const FloatType& theta,
		const FloatType& G,
		const FloatType& softening
	) noexcept {

		if (node == nullptr || node->IsEmpty()) {
			return Vector3<FloatType>(0, 0, 0);
		}

		Vector3<FloatType> centerOfMass = node->MassWeightedPosition / node->Mass;
		Vector3<FloatType> r = centerOfMass - particlePos;
		FloatType distance = r.Norm();
		FloatType softenedDistance = std::sqrt(distance * distance + softening * softening);

		if (node->IsLeaf()) {
			if (distance < softening * FloatType(0.1)) {
				return Vector3<FloatType>(0, 0, 0);
			}
			FloatType forceMagnitude = G * particleMass * node->Mass / (softenedDistance * softenedDistance * softenedDistance);
			return r * forceMagnitude;
		}

		FloatType nodeSize = node->Size();
		if (nodeSize / distance < theta) {
			FloatType forceMagnitude = G * particleMass * node->Mass / (softenedDistance * softenedDistance * softenedDistance);
			return r * forceMagnitude;
		}

		Vector3<FloatType> totalForce(0, 0, 0);
		for (const auto& branch : node->Branches) {
			if (branch != nullptr) {
				totalForce += CalculateForceFromNode(
					particlePos,
					particleMass,
					branch,
					theta,
					G,
					softening
				);
			}
		}

		return totalForce;
	}

	template<class FloatType>
	void UpdateAccelerationRecursive(
		Leaf<FloatType>* node,
		Leaf<FloatType>* root,
		Vector3<FloatType>* accelerations,
		Vector3<FloatType>* positions,
		FloatType* masses,
		FloatType theta,
		FloatType G,
		FloatType softening
	) noexcept {
		if (node == nullptr) return;

		if (node->IsLeaf()) {
			size_t particleIndex = node->ParticleIndex;
			Vector3<FloatType> totalForce = CalculateForceFromNode(
				positions[particleIndex],
				masses[particleIndex],
				root,
				theta,
				G,
				softening
			);
			accelerations[particleIndex] = totalForce / masses[particleIndex];
		}

		for (auto& branch : node->Branches) {
			if (branch != nullptr) {
				UpdateAccelerationRecursive(branch, root, accelerations, positions, masses, theta, G, softening);
			}
		}
	}

	template<class FloatType, size_t Size>
	void UpdateAcceleration(
		Leaf<FloatType>& root,
		CollectionArray<FloatType, Size>& collection,
		FloatType theta = FloatType(0.5),
		FloatType G = FloatType(6.67430e-11),
		FloatType softening = FloatType(1e-3)
	) noexcept {
		for (size_t i = 0; i < Size; ++i) {
			collection.Acceleration[i] = Vector3<FloatType>(0, 0, 0);
		}
		UpdateAccelerationRecursive(&root, &root, collection.Acceleration.data(), collection.Position.data(), collection.Mass.data(), theta, G, softening);
	}

}