#pragma once
#include "Vector.hpp"
#include <array>
#include <vector>
#include <cstddef>

namespace nblib {

	template<class FloatType, size_t Size>
	struct CollectionArray{
		std::array<Vector3<FloatType>, Size> Position;
		std::array<Vector3<FloatType>, Size> Velocity;
		std::array<Vector3<FloatType>, Size> Acceleration;
		std::array<FloatType, Size> Mass;
		std::array<void*, Size> TreeNodes;
	};

	template<class FloatType>
	struct Collection{
		std::vector<Vector3<FloatType>> Position;
		std::vector<Vector3<FloatType>> Velocity;
		std::vector<Vector3<FloatType>> Acceleration;
		std::vector<FloatType> Mass;
		std::vector<void*> TreeNodes;
		
		Collection() = default;

		void PushBack(const FloatType &mass, 
			const Vector3<FloatType> &position, 
			const Vector3<FloatType> &velocity=Vector3<FloatType>(0,0,0), 
			const Vector3<FloatType> &acceleration=Vector3<FloatType>(0,0,0)) {
			
			Mass.push_back(mass);
			Position.push_back(position);
			Velocity.push_back(velocity);
			Acceleration.push_back(acceleration);
			TreeNodes.push_back(nullptr);
		}
		
		inline size_t Size() { return Mass.size(); }
		inline bool IsEmpty() { return Mass.empty(); }
		
		inline void Clear() { Mass.clear(); Position.clear(); Velocity.clear(); Acceleration.clear(); TreeNodes.clear(); }
		inline void Reserve(size_t size) { Mass.reserve(size); Position.reserve(size); Velocity.reserve(size); Acceleration.reserve(size); TreeNodes.reserve(size); }
		inline void Resize(size_t size) { Mass.resize(size); Position.resize(size); Velocity.resize(size); Acceleration.resize(size); TreeNodes.resize(size); }

		
	};
	template<class FloatType, size_t Size>
	struct SPCollectionArray{
		std::array<Vector3<FloatType>, Size> Position;
		std::array<Vector3<FloatType>, Size> Velocity;
		std::array<Vector3<FloatType>, Size> Acceleration;
	};

	template<size_t Size>
	using CollectionF = CollectionArray<float,Size>;
	template<size_t Size>
	using CollectionD = CollectionArray<double,Size>;

	template<size_t Size>
	using SPCollectionF = SPCollectionArray<float,Size>;
	template<size_t Size>
	using SPCollectionD = SPCollectionArray<double,Size>;
}
